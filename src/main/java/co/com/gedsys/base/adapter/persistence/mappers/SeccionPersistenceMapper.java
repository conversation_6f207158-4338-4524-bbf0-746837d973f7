package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.infrastructure.data_access.SeccionEntity;
import co.com.gedsys.base.infrastructure.data_access.UsuarioSeccionEntity;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.UsuarioSeccion;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface SeccionPersistenceMapper {

    @Mapping(target = "hijos", ignore = true)
    Seccion toDomain(SeccionEntity entity);

    @Mapping(target = "codigo", expression = "java(seccion.codigo())")
    @Mapping(target = "usuarios", ignore = true)
    @Mapping(target = "padre", ignore = true)
    SeccionEntity toEntity(Seccion seccion);

    @Mapping(target = "seccion.hijos", ignore = true)
    @Mapping(target = "seccion.usuarios", ignore = true)
    @Mapping(target = "seccion.padre", ignore = true)
    UsuarioSeccion toDomain(UsuarioSeccionEntity entity);

    @Mapping(target = "seccion.usuarios", ignore = true)
    @Mapping(target = "seccion.padre", ignore = true)
    UsuarioSeccionEntity toEntity(UsuarioSeccion usuarioSeccion);
}
